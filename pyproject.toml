[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "paperscraper"
version = "0.1.0"
description = "Fetch arXiv metadata, cache pages, and extract affiliations"
authors = [{name="Your Name"}]
requires-python = ">=3.9"
dependencies = [
  "openai>=1.0.0",
  "requests>=2.31.0",
  "beautifulsoup4>=4.12.0",
  "PyPDF>3.0.0",
  "certifi>=2023.0.0",
  "dotenv"
]

[project.scripts]
paperscraper = "paperscraper.cli:main"
pscraper = "paperscraper.cli:main"

[tool.setuptools.packages.find]
include = ["paperscraper*"]
