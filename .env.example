# OpenAI Configuration
# Required for provider=openai
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Ollama Configuration (optional)
# Used when provider=ollama, defaults to "ollama" if not set
# OLLAMA_API_KEY=ollama

# Model Configuration
# Default model for affiliations extraction
# If not set, defaults to gpt-5-nano for OpenAI and qwen3:4b-instruct for Ollama
# For OpenAI: gpt-5-nano, gpt-4, gpt-3.5-turbo, etc.
# For Ollama: qwen3:4b-instruct, llama2, etc.
# AFFILIATIONS_MODEL=gpt-5-nano

# API Base URLs (optional overrides)
# These are now configurable via environment variables
OPENAI_BASE_URL=https://api.openai.com/v1
OLLAMA_BASE_URL=http://localhost:11434/v1

# Data Directory (optional)
# Override the default data directory location
# DATA_DIR=data

# Logging Level (optional)
# Set to DEBUG, INFO, WARNING, or ERROR
# LOG_LEVEL=INFO
