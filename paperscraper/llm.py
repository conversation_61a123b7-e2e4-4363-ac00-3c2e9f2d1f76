"""
LLM processing for extracting affiliations.

Provides getAffiliations that uses the OpenAI client with selectable provider:
- provider="openai": base_url from OPENAI_BASE_URL env var (default: https://api.openai.com/v1), requires OPENAI_API_KEY
- provider="ollama": base_url from OLLAMA_BASE_URL env var (default: http://localhost:11434/v1), api_key can be any string

Default models:
- OpenAI: gpt-5-nano (can be overridden with AFFILIATIONS_MODEL env var)
- Ollama: qwen3:4b-instruct (can be overridden with AFFILIATIONS_MODEL env var)

Returns a dict with input_tokens, output_tokens, and affiliations (pydantic model serialized to dict).
"""
from __future__ import annotations

from typing import Dict, Optional, List
from pydantic import BaseModel
from openai import OpenAI
import os
import json
import re
from dotenv import load_dotenv

load_dotenv()

# Load organizations once at module import time
with open("orgs.txt", "r", encoding="utf-8") as f:
    ORGS_LIST = f.read().strip()


class AuthorAffiliation(BaseModel):
    author_name: str
    organization: str


class AllAuthorAffiliation(BaseModel):
    author_name: str
    organization: str
    is_notable: bool


class PaperAffilationAnalysis(BaseModel):
    paper_title: str
    all_affiliations: List[AllAuthorAffiliation]
    notable_affiliations: List[AuthorAffiliation]
    has_notable_affiliations: bool


# Get base URLs from environment variables with fallback defaults
OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434/v1")
OPENAI_BASE = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

# Default models for each provider
DEFAULT_OPENAI_MODEL = "gpt-5-nano"
DEFAULT_OLLAMA_MODEL = "qwen3:4b-instruct"


def _get_client(provider: str = "openai", api_key: Optional[str] = None, base_url: Optional[str] = None) -> OpenAI:
    provider = (provider or "openai").lower()
    if provider == "ollama":
        return OpenAI(base_url=base_url or OLLAMA_BASE, api_key="ollama")
    # default openai
    key = api_key or os.getenv("OPENAI_API_KEY")
    if not key:
        raise RuntimeError("OPENAI_API_KEY is required for provider 'openai'.")
    return OpenAI(base_url=base_url or OPENAI_BASE, api_key=key)

def parse_output(completion, provider):

    if provider in {"openai", "ollama"}:
        return {
            "input_tokens": completion.usage.prompt_tokens,
            "output_tokens": completion.usage.completion_tokens,
            "affiliations": completion.choices[0].message.parsed.model_dump()
        }

    return {}


def getAffiliations(paper_title: str, paper_text: str, *, provider: str = "openai", api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None) -> Dict:
    # Use global organizations list loaded once at module import
    valid_organizations = ORGS_LIST

    prompt = f"""
You are tasked with extracting ALL author affiliations from a research paper and determining which ones match the provided notable organizations.

Paper title: {paper_title}

First page text: {paper_text}

Notable organizations to check against:
{valid_organizations}

Extract ALL author affiliations from the paper, then:
1. Mark each affiliation as notable (is_notable: true) if it matches any organization in the provided list
2. Include all affiliations in all_affiliations
3. Include only notable affiliations in notable_affiliations
4. Set has_notable_affiliations to true if any affiliations match the notable organizations list

Return comprehensive affiliation data for all authors.
"""

    client = _get_client(provider=provider, api_key=api_key, base_url=base_url)

    # Choose model based on provider if not specified
    if model:
        chosen_model = model
    elif provider.lower() == "ollama":
        chosen_model = os.getenv("AFFILIATIONS_MODEL", DEFAULT_OLLAMA_MODEL)
    else:  # openai
        chosen_model = os.getenv("AFFILIATIONS_MODEL", DEFAULT_OPENAI_MODEL)
    
    completion = client.beta.chat.completions.parse(
        model=chosen_model,
        messages=[
            {"role": "system", "content": "Extract ALL author affiliations from research papers and identify which match notable organizations."},
            {"role": "user", "content": prompt},
        ],
        response_format=PaperAffilationAnalysis,
    )

    response = parse_output(completion, provider)

    return response
