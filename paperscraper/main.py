#!/usr/bin/env python3
"""
CLI to work with arXiv papers: fetch metadata, cache first pages, and extract affiliations.

This module also serves as the entrypoint for the `paperscraper` console script.
"""
from __future__ import annotations

import argparse
import json
import logging
import os
import sys
import time
from typing import List, Optional

DEFAULT_DATA_DIR = "data"


def configure_logging(verbosity: int) -> None:
    level = logging.WARNING
    if verbosity == 1:
        level = logging.INFO
    elif verbosity >= 2:
        level = logging.DEBUG
    logging.basicConfig(level=level, format="%(levelname)s: %(message)s")



def _date_parts(date: str):
    y, m, d = date.split("-")
    return y[2:], m, d


def _category_day_dir(root: str, category: str, date: str) -> str:
    """Function for category-based structure"""
    yy, mm, dd = _date_parts(date)
    return os.path.join(root, category, yy, mm, dd)


def _papers_json_path(root: str, category: str, date: str) -> str:
    """Get path to unified papers.json file"""
    return os.path.join(_category_day_dir(root, category, date), "papers.json")


def _load_papers_json(papers_path: str) -> dict:
    """Load existing papers.json or return empty structure"""
    if os.path.exists(papers_path):
        try:
            with open(papers_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logging.warning("Failed to load existing papers.json: %s", e)
    return {"papers": []}


def _save_papers_json(papers_path: str, data: dict) -> None:
    """Save papers.json with proper structure"""
    os.makedirs(os.path.dirname(papers_path), exist_ok=True)
    with open(papers_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def _dedupe_papers(existing_papers: List[dict], new_papers: List[dict]) -> List[dict]:
    """Deduplicate papers based on abs_url or id"""
    seen = set()
    for paper in existing_papers:
        key = paper.get("abs_url") or paper.get("id")
        if key:
            seen.add(str(key))
    
    deduped = []
    for paper in new_papers:
        key = paper.get("abs_url") or paper.get("id")
        if key and str(key) not in seen:
            deduped.append(paper)
            seen.add(str(key))
    
    return deduped


def _fetch_metadata_for_category(category: str, date: str, limit: int = None) -> List[dict]:
    """Fetch metadata for any arXiv category"""
    from paperscraper.metadata import fetch_metadata_for_category
    return fetch_metadata_for_category(category, date, limit=limit)


def cmd_collect(args: argparse.Namespace) -> int:
    """Unified command that handles metadata, pages, and affiliations in one go"""
    category = args.category
    date = args.date
    data_dir = args.data_dir
    steps = args.steps or ["metadata", "pages", "affiliations"]
    
    papers_path = _papers_json_path(data_dir, category, date)
    
    # Load existing papers
    papers_data = _load_papers_json(papers_path)
    existing_papers = papers_data.get("papers", [])
    
    # Step 1: Metadata
    if "metadata" in steps:
        logging.info("Step 1/3: Fetching metadata for %s on %s", category, date)
        try:
            new_papers = _fetch_metadata_for_category(category, date, limit=args.num)
        except ValueError as e:
            print(f"Error: {e}", file=sys.stderr)
            return 2
        except Exception as e:
            print(f"Network/API error: {e}", file=sys.stderr)
            return 4
        
        if not new_papers and not existing_papers:
            print("No papers found for that date.")
            return 3
        
        # Deduplicate and merge
        deduped_new = _dedupe_papers(existing_papers, new_papers)
        all_papers = existing_papers + deduped_new
        
        # Update papers data
        papers_data = {
            "date": date,
            "category": category,
            "count": len(all_papers),
            "papers": all_papers
        }
        
        # Save updated data
        try:
            _save_papers_json(papers_path, papers_data)
            logging.info("Added %d new papers (%d total)", len(deduped_new), len(all_papers))
        except Exception as e:
            print(f"Failed to save papers.json: {e}", file=sys.stderr)
            return 5
    
    # Reload papers data for subsequent steps
    papers_data = _load_papers_json(papers_path)
    papers = papers_data.get("papers", [])
    
    if not papers:
        print("No papers available to process.")
        return 3
    
    # Step 2: Pages
    if "pages" in steps:
        logging.info("Step 2/3: Fetching first page content")
        from paperscraper.page import getFirstPageText
        
        updated = False
        for i, paper in enumerate(papers):
            if paper.get("first_page_text"):
                continue  # Skip if already has page text
            
            logging.info("Processing page %d/%d: %s", i+1, len(papers), paper.get("title", "(untitled)"))
            
            try:
                page_info = getFirstPageText(paper)
                if page_info.get("text"):
                    paper["first_page_text"] = page_info["text"]
                    paper["first_page_source"] = page_info.get("source", "pdf")
                    updated = True
                else:
                    logging.warning("Empty page text for paper: %s", paper.get("title", ""))
            except Exception as e:
                logging.error("Failed to get page text for %s: %s", paper.get("title", ""), e)
        
        if updated:
            try:
                _save_papers_json(papers_path, papers_data)
                logging.info("Updated papers with first page content")
            except Exception as e:
                print(f"Failed to save updated papers.json: {e}", file=sys.stderr)
                return 5
    
    # Step 3: Affiliations
    if "affiliations" in steps:
        logging.info("Step 3/3: Processing affiliations")
        from paperscraper.llm import getAffiliations
        
        updated = False
        for i, paper in enumerate(papers):
            if paper.get("affiliations"):
                continue  # Skip if already has affiliations
            
            logging.info("Processing affiliations %d/%d: %s", i+1, len(papers), paper.get("title", "(untitled)"))
            
            try:
                aff_start = time.time()
                aff_resp = getAffiliations(
                    paper_title=paper.get("title", ""),
                    paper_text=paper.get("first_page_text", ""),
                    provider=args.provider,
                    api_key=args.api_key,
                    base_url=args.base_url,
                    model=args.model,
                )
                aff_end = time.time()
                
                paper["affiliations"] = aff_resp.get("affiliations")
                paper["input_tokens"] = aff_resp.get("input_tokens", 0)
                paper["output_tokens"] = aff_resp.get("output_tokens", 0)
                paper["affiliation_processing_time"] = round(aff_end - aff_start, 2)
                updated = True
                
            except Exception as e:
                logging.error("Failed to get affiliations for %s: %s", paper.get("title", ""), e)
        
        if updated:
            try:
                _save_papers_json(papers_path, papers_data)
                logging.info("Updated papers with affiliations")
            except Exception as e:
                print(f"Failed to save updated papers.json: {e}", file=sys.stderr)
                return 5
    
    # Print summary
    print(f"Processing complete! Results saved to: {papers_path}")
    print(f"Total papers: {len(papers)}")
    
    # Print some URLs for convenience
    for paper in papers[:5]:  # Show first 5
        if paper.get("abs_url"):
            print(paper["abs_url"])
    
    return 0




def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="Collect arXiv papers: metadata, pages, and affiliations in one unified workflow.")
    
    # Only support the collect command
    p.add_argument("category", help="arXiv category (e.g., cs.AI, cs.CV, cs.CL, cs.LG, math.CO, physics.optics)")
    p.add_argument("date", help="Date in YYYY-MM-DD format")
    
    p.add_argument("--data-dir", default=DEFAULT_DATA_DIR, help="Root data directory (default: data)")
    p.add_argument("--verbose", "-v", action="count", default=0, help="Increase logging verbosity (-v, -vv)")

    # Processing options
    p.add_argument("--steps", help="Comma-separated steps: metadata,pages,affiliations (default: all)")
    p.add_argument("--num", type=int, help="Max number of papers to fetch (if not specified, will prompt for confirmation to process all)")
    p.add_argument("-y", "--yes", action="store_true", help="Auto-confirm prompts (skip confirmation for processing all papers)")

    # LLM options for affiliations step
    p.add_argument("--provider", choices=["openai", "ollama"], default="openai", help="LLM provider")
    p.add_argument("--api-key", help="API key (ignored for ollama; default env vars used)")
    p.add_argument("--base-url", help="Override base URL (optional)")
    p.add_argument("--model", help="Model name (default via AFFILIATIONS_MODEL env var)")

    return p


def main(argv: List[str]) -> int:
    parser = build_parser()
    args = parser.parse_args(argv)
    configure_logging(args.verbose)

    # Parse steps if provided
    if args.steps:
        args.steps = [step.strip() for step in args.steps.split(",")]
        # Validate steps
        valid_steps = {"metadata", "pages", "affiliations"}
        for step in args.steps:
            if step not in valid_steps:
                print(f"Invalid step: {step}. Valid steps: {', '.join(valid_steps)}", file=sys.stderr)
                return 2
    else:
        args.steps = ["metadata", "pages", "affiliations"]  # Default to all steps
    
    # Check if user didn't specify num parameter and confirm they want all files
    if args.num is None and not args.yes:
        response = input("No --num parameter specified. Do you want to process ALL papers for this date? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("Operation cancelled. Please specify --num parameter to limit the number of papers.")
            return 1
        # Keep args.num as None to indicate no limit
    elif args.num is None and args.yes:
        # Auto-confirm with -y flag
        print("Auto-confirming: processing ALL papers for this date (--yes flag specified)")
    
    # Check dependencies and add missing steps
    papers_path = _papers_json_path(args.data_dir, args.category, args.date)
    papers_data = _load_papers_json(papers_path)
    existing_papers = papers_data.get("papers", [])
    
    # Check if papers have required data for each step
    has_metadata = len(existing_papers) > 0
    has_pages = has_metadata and any(paper.get("first_page_text") for paper in existing_papers)
    
    steps_to_add = []
    
    # If running affiliations step, ensure metadata and pages are available
    if "affiliations" in args.steps:
        if not has_pages and "pages" not in args.steps:
            steps_to_add.append("pages")
            print("Affiliations step requires page content. Adding 'pages' step...")
        if not has_metadata and "metadata" not in args.steps:
            steps_to_add.append("metadata")
            print("Pages step requires metadata. Adding 'metadata' step...")
    
    # If running pages step, ensure metadata is available
    elif "pages" in args.steps:
        if not has_metadata and "metadata" not in args.steps:
            steps_to_add.append("metadata")
            print("Pages step requires metadata. Adding 'metadata' step...")
    
    # Add missing steps in correct order
    if steps_to_add:
        # Add in reverse order so they appear at the beginning in correct sequence
        for step in reversed(steps_to_add):
            if step not in args.steps:
                args.steps = [step] + args.steps
    
    return cmd_collect(args)


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
