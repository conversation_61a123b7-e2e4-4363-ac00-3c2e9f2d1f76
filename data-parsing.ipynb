{"cells": [{"cell_type": "markdown", "id": "5b79285a", "metadata": {}, "source": ["# Research Author Affiliation Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "bd980714", "metadata": {}, "outputs": [], "source": ["!pip freeze"]}, {"cell_type": "code", "execution_count": null, "id": "36e256d8", "metadata": {}, "outputs": [], "source": ["!pip install pypdf dotenv"]}, {"cell_type": "code", "execution_count": null, "id": "93bcca91", "metadata": {}, "outputs": [], "source": ["!pip3 install -e ."]}, {"cell_type": "code", "execution_count": 1, "id": "77397064", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Date: 2024-08-01\n", "Category: cs.AI\n", "Paper count: 3\n", "---\n", "Title: OmniParser for Pure Vision Based GUI Agent\n", "Authors: <AUTHORS>\n", "Abstract URL: https://arxiv.org/abs/2408.00203\n", "---\n", "2 more papers...\n"]}], "source": ["import json\n", "from pypdf import PdfReader\n", "from io import BytesIO\n", "import requests\n", "\n", "\n", "# Open and load the metadata.json file\n", "with open('data/cs.AI/24/08/01/papers.json', 'r', encoding='utf-8') as f:\n", "    metadata = json.load(f)\n", "\n", "# Access the data\n", "print(f\"Date: {metadata['date']}\")\n", "print(f\"Category: {metadata['category']}\")\n", "print(f\"Paper count: {metadata['count']}\\n---\")\n", "\n", "# Example: iterate through papers\n", "for paper in metadata['papers']:\n", "    print(f\"Title: {paper['title']}\")\n", "    print(f\"Authors: <AUTHORS>\n", "    print(f\"Abstract URL: {paper['abs_url']}\")\n", "    print(\"---\")\n", "    break\n", "\n", "print(f\"{len(metadata['papers'])-1} more papers...\")"]}, {"cell_type": "code", "execution_count": 2, "id": "cfff850e", "metadata": {}, "outputs": [], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import re\n", "from io import BytesIO\n", "from pypdf import PdfReader\n", "\n", "def getHTMLFirstPageText(paper, char_limit=1500):\n", "    arxiv_id = paper[\"abs_url\"].split(\"/\")[-1]\n", "    html_url = f\"https://arxiv.org/html/{arxiv_id}\"\n", "    \n", "    response = requests.get(html_url, timeout=30)\n", "    response.raise_for_status()  # Raises HTTPError for 4xx/5xx\n", "\n", "    soup = BeautifulSoup(response.content, 'html.parser')\n", "    text = soup.get_text()\n", "\n", "    text = re.sub(r'[ \\t]+', ' ', text)\n", "    text = re.sub(r'\\n+', '\\n', text)\n", "\n", "    return text[:char_limit]\n", "\n", "def getPDFFirstPageText(paper, char_limit=1500):\n", "    pdf_url = paper[\"pdf_url\"]\n", "    response = requests.get(pdf_url, timeout=30)\n", "    response.raise_for_status()\n", "\n", "    pdf = PdfReader(BytesIO(response.content))\n", "    text = pdf.pages[0].extract_text()\n", "    return text[:char_limit]\n", "\n", "def getFirstPageText(paper, char_limit=1500):\n", "    arxiv_id = paper[\"abs_url\"].split(\"/\")[-1]\n", "    \n", "    try:\n", "        # Try HTML first\n", "        return {\n", "            \"source\": \"html\",\n", "            \"text\": getHTMLFirstPageText(paper, char_limit)\n", "        }\n", "    except requests.exceptions.HTTPError as e:\n", "        if e.response.status_code == 404:\n", "            # print(f\"HTML page not found for {arxiv_id} (404), falling back to PDF\")\n", "            return {\n", "                \"source\": \"pdf\",\n", "                \"text\": getPDFFirstPageText(paper, char_limit),\n", "                \"error\": e\n", "            }\n", "        else:\n", "            # print(f\"HTTP error for {arxiv_id}: {e}\")\n", "            return {\n", "                \"source\": \"none\",\n", "                \"text\": \"\",\n", "                \"error\": e\n", "            }\n", "    except requests.exceptions.RequestException as e:\n", "        # print(f\"Request error for {arxiv_id}: {e}\")\n", "        return {\n", "                \"source\": \"none\",\n", "                \"text\": \"\",\n", "                \"error\": e\n", "            }\n"]}, {"cell_type": "code", "execution_count": 1, "id": "0423be82", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'metadata' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 83\u001b[39m\n\u001b[32m     79\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n\u001b[32m     82\u001b[39m paper_index = \u001b[32m1\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m83\u001b[39m paper_title = \u001b[43mmetadata\u001b[49m[\u001b[33m'\u001b[39m\u001b[33mpapers\u001b[39m\u001b[33m'\u001b[39m][paper_index][\u001b[33m'\u001b[39m\u001b[33mtitle\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m     84\u001b[39m paper_text = getFirstPageText(metadata[\u001b[33m'\u001b[39m\u001b[33mpapers\u001b[39m\u001b[33m'\u001b[39m][paper_index])[\u001b[33m\"\u001b[39m\u001b[33mtext\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m     86\u001b[39m response = getAffiliations(paper_title, paper_text)\n", "\u001b[31mNameError\u001b[39m: name 'metadata' is not defined"]}], "source": ["from pydantic import BaseModel\n", "from typing import List, Optional\n", "from openai import OpenAI\n", "import os\n", "\n", "\n", "import dotenv\n", "dotenv.load_dotenv()\n", "\n", "\n", "class AuthorAffiliation(BaseModel):\n", "    author_name: str\n", "    organization: str\n", "\n", "class AllAuthorAffiliation(BaseModel):\n", "    author_name: str\n", "    organization: str\n", "    is_notable: bool\n", "\n", "class PaperAffilationAnalysis(BaseModel):\n", "    paper_title: str\n", "    all_affiliations: List[AllAuthorAffiliation]\n", "    notable_affiliations: List[AuthorAffiliation]\n", "    has_notable_affiliations: bool\n", "\n", "ollama_api_base = \"http://localhost:11434/v1\"\n", "openai_api_base = \"https://api.openai.com/v1\"\n", "\n", "# client = OpenAI(\n", "#     base_url =  openai_api_base,\n", "#     api_key = os.environ[\"OPENAI_API_KEY\"]\n", "#     )\n", "\n", "client = OpenAI(\n", "    base_url =  ollama_api_base,\n", "    api_key = \"ollama\"\n", "    )\n", "\n", "\n", "\n", "\n", "def getAffiliations(paper_title, paper_text):\n", "    # Load organizations from file\n", "    with open('orgs.txt', 'r') as f:\n", "        valid_organizations = f.read().strip()\n", "\n", "    prompt = f\"\"\"\n", "You are tasked with extracting ALL author affiliations from a research paper and determining which ones match the provided notable organizations.\n", "\n", "Paper title: {paper_title}\n", "\n", "First page text: {paper_text}\n", "\n", "Notable organizations to check against:\n", "{valid_organizations}\n", "\n", "Extract ALL author affiliations from the paper, then:\n", "1. Mark each affiliation as notable (is_notable: true) if it matches any organization in the provided list\n", "2. Include all affiliations in all_affiliations\n", "3. Include only notable affiliations in notable_affiliations  \n", "4. Set has_notable_affiliations to true if any affiliations match the notable organizations list\n", "\n", "Return comprehensive affiliation data for all authors.\n", "\"\"\"\n", "\n", "    response = client.chat.completions.parse(\n", "        # model=\"gpt-5-nano\",\n", "        model = \"gemma3:4b\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"Extract ALL author affiliations from research papers and identify which match notable organizations.\"},\n", "            {\n", "                \"role\": \"user\", \n", "                \"content\": prompt\n", "                }\n", "        ],\n", "        response_format=PaperAffilationAnalysis,\n", "    )\n", "\n", "    return response\n", "\n", "\n", "paper_index = 1\n", "paper_title = metadata['papers'][paper_index]['title']\n", "paper_text = getFirstPageText(metadata['papers'][paper_index])[\"text\"]\n", "\n", "response = getAffiliations(paper_title, paper_text)\n", "# response[\"affiliations\"]\n", "response"]}, {"cell_type": "code", "execution_count": 21, "id": "5bbc8441", "metadata": {}, "outputs": [{"data": {"text/plain": ["364"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["response.usage.prompt_tokens\n", "response.usage.completion_tokens"]}, {"cell_type": "code", "execution_count": 31, "id": "41597e66", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'paper_title': 'A Prior Embedding-Driven Architecture for Long Distance Blind Iris Recognition',\n", " 'all_affiliations': [{'author_name': '<PERSON>',\n", "   'organization': 'International College, Hunan University of Arts and Sciences, Changde 415000, China',\n", "   'is_notable': <PERSON><PERSON><PERSON>},\n", "  {'author_name': '<PERSON><PERSON><PERSON>',\n", "   'organization': 'School of Automation Science and Engineering, Faculty of Electronic and Information Engineering; MOE Key Lab for Intelligent Networks and Network Security, Xi’an Jiaotong University, Xi’an 710049, Shaanxi, China',\n", "   'is_notable': <PERSON><PERSON><PERSON>},\n", "  {'author_name': '<PERSON>',\n", "   'organization': 'School of Computing and Information Technology, University of Wollongong, Wollongong, NSW 2522, Australia',\n", "   'is_notable': False}],\n", " 'notable_affiliations': [{'author_name': '<PERSON>',\n", "   'organization': 'International College, Hunan University of Arts and Sciences, Changde 415000, China'},\n", "  {'author_name': '<PERSON><PERSON><PERSON>',\n", "   'organization': 'School of Automation Science and Engineering, Faculty of Electronic and Information Engineering; MOE Key Lab for Intelligent Networks and Network Security, Xi’an Jiaotong University, Xi’an 710049, Shaanxi, China'},\n", "  {'author_name': '<PERSON>',\n", "   'organization': 'School of Computing and Information Technology, University of Wollongong, Wollongong, NSW 2522, Australia'}],\n", " 'has_notable_affiliations': <PERSON><PERSON><PERSON>}"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["ob = response.choices[0].message.parsed\n", "ob.model_dump()"]}, {"cell_type": "code", "execution_count": null, "id": "195d2e76", "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "import time\n", "\n", "\n", "affiliations = []\n", "\n", "papers = metadata['papers'][:10]\n", "\n", "for paper_index in tqdm(range(len(papers))):\n", "    \n", "    print(f\"Processing paper {paper_index} of {len(papers)}: {papers[paper_index]['title']}\")\n", "\n", "    total_start = time.time()\n", "    paper_title = metadata['papers'][paper_index]['title']\n", "    get_first_page_text_response = getFirstPageText(metadata['papers'][paper_index])\n", "    paper_text = get_first_page_text_response[\"text\"]\n", "    paper_source = get_first_page_text_response[\"source\"]\n", "    first_page_end = time.time()\n", "\n", "    response = getAffiliations(paper_title, paper_text)\n", "\n", "    total_end = time.time()\n", "\n", "    record = {\n", "        **metadata['papers'][paper_index],\n", "        \"affiliations\": response[\"affiliations\"],\n", "        \"input_tokens\": response[\"input_tokens\"],\n", "        \"output_tokens\": response[\"output_tokens\"],\n", "        \"total_processing_time\": total_end - total_start,\n", "        \"first_page_processing_time\": first_page_end - total_start,\n", "        \"affiliation_processing_time\": total_end - first_page_end,\n", "        \"paper_source\": paper_source\n", "    }\n", "\n", "    affiliations.append(record)\n", "    \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f656565b", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def getAverageInputTokens(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"input_tokens\"]\n", "    # to 2 decimal places\n", "    return round(total / len(affiliations), 2)\n", "\n", "def getAverageOutputTokens(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"output_tokens\"]\n", "    \n", "    # to 2 decimal places\n", "    return round(total / len(affiliations), 2)\n", "\n", "def getAverageTotalProcessingTime(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"total_processing_time\"]\n", "\n", "    # to 2 decimal places\n", "    return round(total / len(affiliations), 2)\n", "\n", "\n", "def getAverageFirstPageProcessingTime(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"first_page_processing_time\"]\n", "    # to 4 decimal places\n", "    return round(total / len(affiliations), 2)\n", "\n", "\n", "def getAverageAffiliationProcessingTime(affiliations):\n", "    total = 0\n", "    for affiliation in affiliations:\n", "        total += affiliation[\"affiliation_processing_time\"]\n", "    return round(total / len(affiliations),2)\n", "\n", "def getSourceCounts(affiliations):\n", "    counts = {}\n", "    for affiliation in affiliations:\n", "        source = affiliation[\"paper_source\"]\n", "        if source in counts:\n", "            counts[source] += 1\n", "        else:\n", "            counts[source] = 1\n", "    return counts\n", "\n", "def getAverageTokensPerSource(affiliations):\n", "    # gets average input and output tokens per source\n", "    source_counts = getSourceCounts(affiliations)\n", "    source_input_tokens = {}\n", "    source_output_tokens = {}\n", "    \n", "    for affiliation in affiliations:\n", "        source = affiliation[\"paper_source\"]\n", "        source_input_tokens[source] = source_input_tokens.get(source, 0) + affiliation[\"input_tokens\"]\n", "        source_output_tokens[source] = source_output_tokens.get(source, 0) + affiliation[\"output_tokens\"]\n", "    \n", "    # Calculate averages\n", "    source_avg_input = {}\n", "    source_avg_output = {}\n", "    for source in source_counts:\n", "        source_avg_input[source] = round(source_input_tokens[source] / source_counts[source], 2)\n", "        source_avg_output[source] = round(source_output_tokens[source] / source_counts[source], 2)\n", "    \n", "    return source_avg_input, source_avg_output\n", "\n", "def getCost(affiliations, price_per_input_token=0.05/1000000, price_per_output_token=0.4/1000000):\n", "    total_cost = 0\n", "    for affiliation in affiliations:\n", "        total_cost += affiliation[\"input_tokens\"] * price_per_input_token + affiliation[\"output_tokens\"] * price_per_output_token\n", "    # to 4 decimal places\n", "    return round(total_cost, 6)\n", "\n", "def printStatistics(affiliations):\n", "    print(\"Number of affiliations: \",len(affiliations))\n", "    print(\"Average input tokens: \",getAverageInputTokens(affiliations))\n", "    print(\"Average output tokens: \",getAverageOutputTokens(affiliations))\n", "    print(\"Average total processing time: \",getAverageTotalProcessingTime(affiliations))\n", "    print(\"Average first page processing time: \",getAverageFirstPageProcessingTime(affiliations))\n", "    print(\"Average affiliation processing time: \",getAverageAffiliationProcessingTime(affiliations))\n", "    print(\"Source counts: \")\n", "    for source in getSourceCounts(affiliations):\n", "        print(f\"\\t{source} \\t{getSourceCounts(affiliations)[source]}\")\n", "\n", "    # print each source, average input tokens, output tokens on new line and tab\n", "    source_avg_input, source_avg_output = getAverageTokensPerSource(affiliations)\n", "    print(\"Average tokens per source:\")\n", "    for source in source_avg_input:\n", "        print(f\"\\t{source}\\t{source_avg_input[source]}\\t{source_avg_output[source]}\")\n", "\n", "    print(\"Total cost: \",getCost(affiliations))\n", "\n", "printStatistics(affiliations)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}