# Paper Scraper - New Unified Data System

## Overview

The paper scraper has been redesigned with a unified data structure that consolidates metadata, first page content, and affiliation classification into a single JSON file per category/date combination.

## New Data Structure

```
data/<category>/YY/MM/DD/papers.json
```

### Example Structure
```
data/
├── cs.AI/
│   └── 24/
│       └── 08/
│           └── 01/
│               └── papers.json
└── cs.CV/
    └── 24/
        └── 08/
            └── 01/
                └── papers.json
```

## Unified JSON Format

Each `papers.json` file contains:

```json
{
  "date": "2024-08-01",
  "category": "cs.AI",
  "count": 3,
  "papers": [
    {
      "id": "2408.00203v1",
      "abs_url": "https://arxiv.org/abs/2408.00203",
      "pdf_url": "https://arxiv.org/pdf/2408.00203v1",
      "title": "Paper Title",
      "summary": "Paper abstract...",
      "published": "2024-08-01T00:00:43Z",
      "updated": "2024-08-01T00:00:43Z",
      "authors": ["Author 1", "Author 2"],
      "primary_category": "cs.CV",
      "categories": ["cs.CV", "cs.AI", "cs.CL"],
      
      // Added by pages step
      "first_page_text": "Extracted first page content...",
      "first_page_source": "pdf",
      
      // Added by affiliations step
      "affiliations": [
        {"author": "Author 1", "affiliation": "University X"},
        {"author": "Author 2", "affiliation": "Company Y"}
      ],
      "input_tokens": 1500,
      "output_tokens": 200,
      "affiliation_processing_time": 1.2
    }
  ]
}
```

## New Unified Command

### Basic Usage

```bash
# Collect metadata, pages, and affiliations (all steps)
paperscraper cs.AI 2024-08-01 --num 10

# Only collect metadata
paperscraper cs.AI 2024-08-01 --steps metadata --num 10

# Only process pages for existing papers
paperscraper cs.AI 2024-08-01 --steps pages

# Only process affiliations for existing papers
paperscraper cs.AI 2024-08-01 --steps affiliations

# Run specific steps
paperscraper cs.AI 2024-08-01 --steps metadata,pages --num 5

# Force refresh existing data
paperscraper cs.AI 2024-08-01 --force
```

### Command Options

- `<category>`: arXiv category (e.g., cs.AI, cs.CV, cs.CL)
- `<date>`: Date in YYYY-MM-DD format
- `--steps`: Comma-separated list of steps (metadata, pages, affiliations)
- `--num`: Maximum number of papers to fetch (default: 5)
- `--force`: Force refresh existing data
- `--verbose, -v`: Increase logging verbosity

### LLM Options (for affiliations step)

- `--provider`: LLM provider (openai, ollama)
- `--api-key`: API key for OpenAI
- `--model`: Model name
- `--orgs`: Path to organizations list file

## Key Features

### 1. **Smart Deduplication**
- Automatically detects existing papers in the JSON file
- Only adds new papers when fetching metadata
- Prevents duplicate entries based on abs_url or paper ID

### 2. **Incremental Processing**
- Skips papers that already have required data
- Only processes missing `first_page_text` or `affiliations`
- Use `--force` to reprocess existing data

### 3. **Single Source of Truth**
- All paper data stored in one JSON file per category/date
- No need to join data from multiple files
- Easier analysis and data management

### 4. **Multi-Category Support**
- Each category gets its own directory tree
- Papers sourced via cs.AI go to `data/cs.AI/...`
- Papers sourced via cs.CV go to `data/cs.CV/...`

## Migration from Legacy System

The legacy system used separate commands for each step:

```bash
# Old system (no longer supported)
paperscraper metadata 2024-08-01
paperscraper pages 2024-08-01
paperscraper affiliations --date 2024-08-01

# New unified approach
paperscraper cs.AI 2024-08-01
```

### Automatic Migration

Run the migration script to convert existing legacy data:

```bash
python migrate_data.py
```

This converts:
- `data/metadata/24/08/01/metadata.json` → `data/cs.AI/24/08/01/papers.json`

## Example Workflows

### 1. Daily Paper Collection
```bash
# Collect today's cs.AI papers with full processing
paperscraper cs.AI $(date +%Y-%m-%d) --num 20 -v
```

### 2. Batch Processing Multiple Categories
```bash
# Collect from multiple categories
paperscraper cs.AI 2024-08-01 --num 10
paperscraper cs.CV 2024-08-01 --num 10
paperscraper cs.CL 2024-08-01 --num 10
```

### 3. Incremental Updates
```bash
# First run: get metadata + pages
paperscraper cs.AI 2024-08-01 --steps metadata,pages --num 15

# Later: add affiliations processing
paperscraper cs.AI 2024-08-01 --steps affiliations
```

### 4. Data Analysis
```python
import json

# Load unified data
with open('data/cs.AI/24/08/01/papers.json', 'r') as f:
    data = json.load(f)

papers = data['papers']
print(f"Total papers: {data['count']}")

# Analyze papers with affiliations
papers_with_affiliations = [p for p in papers if p.get('affiliations')]
print(f"Papers with affiliations: {len(papers_with_affiliations)}")
```

## Benefits of New System

1. **Simplified Workflow**: One command does everything
2. **Better Data Integrity**: All related data stays together
3. **Efficient Deduplication**: Automatic duplicate detection
4. **Incremental Processing**: Only process what's missing
5. **Easier Analysis**: Single file contains all data
6. **Multi-Category Support**: Organized by source category
7. **Clean Architecture**: No legacy code or deprecated commands

## File Structure Comparison

### Old Structure
```
data/
├── metadata/24/08/01/metadata.json
├── pages/24/08/01/2408.00203.txt
├── pages/24/08/01/2408.00210.txt
└── affiliations/24/08/01/affiliations.json
```

### New Structure
```
data/
└── cs.AI/24/08/01/papers.json  # Everything in one file
```

The new system is more efficient, easier to use, and provides better data organization for analysis and processing.
